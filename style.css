* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.calculator-container {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    max-width: 400px;
    width: 100%;
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.calculator {
    margin-bottom: 30px;
}

.display {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    border: 2px solid #e9ecef;
}

#expression {
    width: 100%;
    border: none;
    background: transparent;
    font-size: 1.5em;
    text-align: right;
    color: #333;
    margin-bottom: 10px;
    outline: none;
}

#result {
    font-size: 1.8em;
    font-weight: bold;
    color: #28a745;
    text-align: right;
}

.buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
}

.btn {
    padding: 20px;
    border: none;
    border-radius: 10px;
    font-size: 1.2em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s;
    background: #e9ecef;
    color: #333;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn:active {
    transform: translateY(0);
}

.btn.special {
    background: #ff6b35;
    color: white;
}

.btn.operator {
    background: #007bff;
    color: white;
}

.btn.equals {
    background: #28a745;
    color: white;
}

.btn.wide {
    grid-column: span 2;
}

.history {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.history h3 {
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.history-item {
    background: white;
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    font-family: monospace;
}

.clear-history {
    width: 100%;
    margin-top: 15px;
    background: #dc3545 !important;
    color: white !important;
}

#history-list:empty::after {
    content: "No calculations yet";
    color: #6c757d;
    font-style: italic;
    display: block;
    text-align: center;
    padding: 20px;
}