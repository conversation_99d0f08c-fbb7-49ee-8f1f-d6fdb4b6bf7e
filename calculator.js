let currentExpression = '';
let history = JSON.parse(localStorage.getItem('calculatorHistory')) || [];

// Load history on page load
document.addEventListener('DOMContentLoaded', function() {
    displayHistory();
});

function appendToExpression(value) {
    if (value === '√') {
        currentExpression += 'Math.sqrt(';
    } else {
        currentExpression += value;
    }
    updateDisplay();
}

function updateDisplay() {
    document.getElementById('expression').value = currentExpression.replace(/Math\.sqrt\(/g, '√(');
    
    // Try to evaluate and show preview
    try {
        if (currentExpression && currentExpression !== '') {
            let result = evaluateExpression(currentExpression);
            document.getElementById('result').textContent = '= ' + result;
        }
    } catch (e) {
        document.getElementById('result').textContent = '= 0';
    }
}

function evaluateExpression(expr) {
    // Replace sqrt function
    expr = expr.replace(/√\(/g, 'Math.sqrt(');
    
    // Basic security check - only allow numbers, operators, and Math functions
    if (!/^[0-9+\-*/.() Math.sqrt]+$/.test(expr)) {
        throw new Error('Invalid expression');
    }
    
    return Function('"use strict"; return (' + expr + ')')();
}

function calculate() {
    if (!currentExpression) return;
    
    try {
        const result = evaluateExpression(currentExpression);
        const displayExpression = currentExpression.replace(/Math\.sqrt\(/g, '√(');
        
        // Add to history
        const historyItem = {
            expression: displayExpression,
            result: result,
            timestamp: new Date().toLocaleString()
        };
        
        history.unshift(historyItem);
        
        // Keep only last 50 calculations
        if (history.length > 50) {
            history = history.slice(0, 50);
        }
        
        // Save to localStorage
        localStorage.setItem('calculatorHistory', JSON.stringify(history));
        
        // Update display
        currentExpression = result.toString();
        updateDisplay();
        displayHistory();
        
    } catch (error) {
        document.getElementById('result').textContent = 'Error';
        setTimeout(() => {
            document.getElementById('result').textContent = '= 0';
        }, 2000);
    }
}

function clearAll() {
    currentExpression = '';
    document.getElementById('expression').value = '';
    document.getElementById('result').textContent = '= 0';
}

function deleteLast() {
    if (currentExpression.endsWith('Math.sqrt(')) {
        currentExpression = currentExpression.slice(0, -10);
    } else {
        currentExpression = currentExpression.slice(0, -1);
    }
    updateDisplay();
}

function displayHistory() {
    const historyList = document.getElementById('history-list');
    historyList.innerHTML = '';
    
    history.forEach((item, index) => {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        historyItem.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>${item.expression} = ${item.result}</span>
                <small style="color: #6c757d;">${item.timestamp}</small>
            </div>
        `;
        
        // Click to reuse calculation
        historyItem.style.cursor = 'pointer';
        historyItem.onclick = () => {
            currentExpression = item.result.toString();
            updateDisplay();
        };
        
        historyList.appendChild(historyItem);
    });
}

function clearHistory() {
    if (confirm('Are you sure you want to clear all history?')) {
        history = [];
        localStorage.removeItem('calculatorHistory');
        displayHistory();
    }
}

// Keyboard support
document.addEventListener('keydown', function(event) {
    const key = event.key;
    
    if (key >= '0' && key <= '9') {
        appendToExpression(key);
    } else if (['+', '-', '*', '/', '(', ')', '.'].includes(key)) {
        appendToExpression(key);
    } else if (key === 'Enter' || key === '=') {
        event.preventDefault();
        calculate();
    } else if (key === 'Escape' || key === 'c' || key === 'C') {
        clearAll();
    } else if (key === 'Backspace') {
        event.preventDefault();
        deleteLast();
    }
});