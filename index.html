<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Calculator</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="calculator-container">
        <h1>Web Calculator</h1>
        
        <div class="calculator">
            <div class="display">
                <input type="text" id="expression" readonly placeholder="0">
                <div id="result">= 0</div>
            </div>
            
            <div class="buttons">
                <button class="btn special" onclick="clearAll()">C</button>
                <button class="btn" onclick="deleteLast()">←</button>
                <button class="btn" onclick="appendToExpression('/')">/</button>
                <button class="btn" onclick="appendToExpression('√')">√</button>
                
                <button class="btn" onclick="appendToExpression('7')">7</button>
                <button class="btn" onclick="appendToExpression('8')">8</button>
                <button class="btn" onclick="appendToExpression('9')">9</button>
                <button class="btn operator" onclick="appendToExpression('*')">*</button>
                
                <button class="btn" onclick="appendToExpression('4')">4</button>
                <button class="btn" onclick="appendToExpression('5')">5</button>
                <button class="btn" onclick="appendToExpression('6')">6</button>
                <button class="btn" onclick="appendToExpression('(')">(</button>
                
                <button class="btn" onclick="appendToExpression('0')">0</button>
                <button class="btn" onclick="appendToExpression('1')">1</button>
                <button class="btn" onclick="appendToExpression('2')">2</button>
                <button class="btn" onclick="appendToExpression('3')">3</button>
                <button class="btn operator" onclick="appendToExpression('-')">-</button>
                <button class="btn" onclick="appendToExpression(')')">) </button>
                
                <button class="btn" onclick="appendToExpression('00')">00</button>
                <button class="btn" onclick="appendToExpression('.')">.</button>
                <button class="btn operator wide" onclick="appendToExpression('+')">+</button>
                <button class="btn equals" onclick="calculate()">=</button>
            </div>
        </div>
        
        <div class="history">
            <h3>History</h3>
            <div id="history-list"></div>
            <button class="btn clear-history" onclick="clearHistory()">Clear History</button>
        </div>
    </div>
    
    <script src="calculator.js"></script>
</body>
</html>